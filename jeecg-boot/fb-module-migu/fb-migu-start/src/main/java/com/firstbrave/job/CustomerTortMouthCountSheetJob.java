package com.firstbrave.job;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.entity.customer.FbmCustomer;
import org.jeecg.entity.customer.FbmCustomerCount;
import org.jeecg.entity.FbmCustomerWorksAttachment;
import org.jeecg.entity.FbmWorks;
import org.jeecg.modules.platform.service.impl.*;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cn.hutool.core.collection.ListUtil.partition;

*
 * @Author: ggr
 * @CreateTime: 2025-08-28
 * @Description: 客户管理图表数据统计
 * @Version: 1.0



@Component
@Slf4j
public class CustomerTortMouthCountSheetJob {
    @Resource
    FbmTortDataAudioServiceImpl fbmTortDataAudioService;
    @Resource
    FbmTortDataTxtServiceImpl fbmTortDataTxtService;
    @Resource
    FbmTortDataPicServiceImpl fbmTortDataPicService;
    @Resource
    FbmTortDataIptvServiceImpl fbmTortDataIptvService;
    @Resource
    FbmCustomerCountServiceImpl fbmCustomerCountService;

    @Resource
    FbmCustomerWorksAttachmentServiceImpl fbmCustomerWorksAttachmentService;

    @Resource
    FbmTortDataVideoServiceImpl fbmTortDataVideoService;
    @Resource
    FbmWorksServiceImpl fbmWorksService;
    @Resource
    FbmCustomerServiceImpl fbmCustomerService;
    @XxlJob(value = "CustomerTortMouthCountSheetJob")
    @ApiOperation(value = "", notes = "客户管理数据统计")
    @PostMapping(value = "/customerJob/CustomerTortMouthCountSheetJob")
    public ReturnT<String> run(String params) throws Exception {
        fbmCustomerCountService.remove(null);
        List<FbmCustomer> list = fbmCustomerService.list();
        for (FbmCustomer customer : list) {
            long customerId = customer.getId();
            //获取作品id
            List<Object> worksIds = fbmCustomerWorksAttachmentService.listObjs(
                    Wrappers.<FbmCustomerWorksAttachment>query()
                            .select("DISTINCT works_id")
                            .eq("status", "1")
            );
            if (CollUtil.isEmpty(worksIds)) {
                //保存
                fbmCustomerCountService.saveOrUpdate(
                        new FbmCustomerCount().setCustomerId(customerId)
                                .setWorksCount("0")
                                .setTortCount("0")
                                .setSiteCount("0")
                );
                continue;
            }
            // 按照分类分组统计
            Map<String, List<Long>> categoryWorksMap = fbmWorksService.list(
                            Wrappers.<FbmWorks>lambdaQuery()
                                    .in(FbmWorks::getId, worksIds)
                    ).stream()
                    .collect(Collectors.groupingBy(
                            FbmWorks::getFirstCategory,
                            Collectors.mapping(
                                    FbmWorks::getId,
                                    Collectors.toList()
                            )
                    ));
            //根据分类作品id统计侵权数
//            Long numJan = 0L;
//            Long numFeb = 0L;
//            Long numMar = 0L;
//            Long numApr = 0L;
//            Long numMay = 0L;
//            Long numJun = 0L;
//            Long numJul = 0L;
//            Long numAug = 0L;
//            Long numSep = 0L;
//            Long numOct = 0L;
//            Long numNov = 0L;
//            Long numDec = 0L;
            ArrayList<Long> longs = new ArrayList<>(12);
            for (String category : categoryWorksMap.keySet()) {
                List<Long> worksIdList = categoryWorksMap.get(category);
                if (CollUtil.isEmpty(worksIdList)) {
                    continue;
                }
                //集合分割
                List<List<Long>> partitions = partition(worksIdList, 100);
                for (List<Long> partition : partitions) {
                    switch (category) {
                        case "1":
                            Map<String, Long> countNum = fbmTortDataVideoService.getCountNum(partition);
                            siteCount += countNum.get("siteCount");
                            tortCount += countNum.get("tortCount");
                            Map<String, Long> countNum1 = fbmTortDataIptvService.getCountNum(partition);
                            siteCount += countNum1.get("tortCount");
                            break;
                        case "2":
                            Map<String, Long> countNum2 = fbmTortDataAudioService.getCountNum(partition);
                            siteCount += countNum2.get("siteCount");
                            tortCount += countNum2.get("tortCount");
                            break;
                        case "3":
                            Map<String, Long> countNum3 = fbmTortDataTxtService.getCountNum(partition);
                            siteCount += countNum3.get("siteCount");
                            tortCount += countNum3.get("tortCount");
                            break;
                        case "4":
                            Map<String, Long> countNum4 = fbmTortDataPicService.getCountNum(partition);
                            siteCount += countNum4.get("siteCount");
                            tortCount += countNum4.get("tortCount");
                            break;
                        default:
                            break;
                    }
                }
            }
            //保存
            fbmCustomerCountService.saveOrUpdate(
                    new FbmCustomerCount().setCustomerId(customerId)
                            .setWorksCount(String.valueOf(worksIds.size()))
                            .setTortCount(String.valueOf(tortCount))
                            .setSiteCount(String.valueOf(siteCount))
            );

        }
        return ReturnT.SUCCESS;
    }
}
