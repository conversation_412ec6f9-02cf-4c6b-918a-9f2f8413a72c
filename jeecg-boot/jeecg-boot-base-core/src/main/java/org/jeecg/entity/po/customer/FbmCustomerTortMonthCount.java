package org.jeecg.entity.po.customer;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;

/**
 * @Description: 客户管理页面月份侵权数数据统计
 * @Author: jeecg-boot
 * @Date:   2025-08-28
 * @Version: V1.0
 */
@Data
@TableName("fbm_customer_tort_month_count")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="fbm_customer_tort_month_count对象", description="客户管理页面月份侵权数数据统计")
public class FbmCustomerTortMonthCount {
    
	/**month*/
	@Excel(name = "month", width = 15)
    @ApiModelProperty(value = "month")
	private java.lang.Integer month;
	/**tortCount*/
	@Excel(name = "tortCount", width = 15)
    @ApiModelProperty(value = "tortCount")
	private java.lang.String tortCount;
}
