package org.jeecg.entity.po.customer;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;

/**
 * @Description: 客户管理
 * @Author: jeecg-boot
 * @Date:   2025-08-26
 * @Version: V1.0
 */
@Data
@TableName("fbm_customer")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="fbm_customer对象", description="客户管理")
public class FbmCustomer {
    
	/**id*/
	@TableId(type = IdType.AUTO )
    @ApiModelProperty(value = "id")
	private java.lang.Integer id;
	/**version*/
	@Excel(name = "version", width = 15)
    @ApiModelProperty(value = "version")
	private java.lang.Integer version;
	/**联系人电话*/
	@Excel(name = "联系人电话", width = 15)
    @ApiModelProperty(value = "联系人电话")
	private java.lang.String caoncatTel;
	/**城市*/
	@Excel(name = "城市", width = 15)
    @ApiModelProperty(value = "城市")
	private java.lang.String city;
	/**联系人姓名*/
	@Excel(name = "联系人姓名", width = 15)
    @ApiModelProperty(value = "联系人姓名")
	private java.lang.String concatName;
	/**创建时间,默认为当前*/
	@Excel(name = "创建时间,默认为当前", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间,默认为当前")
	private java.util.Date createTime;
	/**createUserFullName*/
	@Excel(name = "createUserFullName", width = 15)
    @ApiModelProperty(value = "createUserFullName")
	private java.lang.String createUserFullName;
	/**创建者用户ID*/
	@Excel(name = "创建者用户ID", width = 15)
    @ApiModelProperty(value = "创建者用户ID")
	private java.lang.Integer createUserId;
	/**客户名字*/
	@Excel(name = "客户名字", width = 15)
    @ApiModelProperty(value = "客户名字")
	private java.lang.String customerName;
	/**客户所在国家*/
	@Excel(name = "客户所在国家", width = 15)
    @ApiModelProperty(value = "客户所在国家")
	private java.lang.String area;
	/**客户图标*/
	@Excel(name = "客户图标", width = 15)
    @ApiModelProperty(value = "客户图标")
	private java.lang.String imgurl;
	/**客户英文名称*/
	@Excel(name = "客户英文名称", width = 15)
    @ApiModelProperty(value = "客户英文名称")
	private java.lang.String englishName;
	/**描述*/
	@Excel(name = "描述", width = 15)
    @ApiModelProperty(value = "描述")
	private java.lang.String description;
	/**修改时间,默认为当前*/
	@Excel(name = "修改时间,默认为当前", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "修改时间,默认为当前")
	private java.util.Date modifyTime;
	/**省份*/
	@Excel(name = "省份", width = 15)
    @ApiModelProperty(value = "省份")
	private java.lang.String province;
	/**明抄送邮箱*/
	@Excel(name = "明抄送邮箱", width = 15)
    @ApiModelProperty(value = "明抄送邮箱")
	private java.lang.String ccmail;
	/**密抄送邮箱*/
	@Excel(name = "密抄送邮箱", width = 15)
    @ApiModelProperty(value = "密抄送邮箱")
	private java.lang.String bcc;
	/**是否接收下线函，1：接收（默认） 0：不接收*/
	@Excel(name = "是否接收下线函，1：接收（默认） 0：不接收", width = 15)
    @ApiModelProperty(value = "是否接收下线函，1：接收（默认） 0：不接收")
	private java.lang.Integer isReceive;
	/**客户全部名字*/
	@Excel(name = "客户全部名字", width = 15)
    @ApiModelProperty(value = "客户全部名字")
	private java.lang.String customerFullName;
	/**客户简称*/
	@Excel(name = "客户简称", width = 15)
    @ApiModelProperty(value = "客户简称")
	private java.lang.String customerShortName;
	/**客户地址*/
	@Excel(name = "客户地址", width = 15)
    @ApiModelProperty(value = "客户地址")
	private java.lang.String customerAddress;
	/**法定代表人姓名*/
	@Excel(name = "法定代表人姓名", width = 15)
    @ApiModelProperty(value = "法定代表人姓名")
	private java.lang.String legalRepresentative;
	/**营业执照编号*/
	@Excel(name = "营业执照编号", width = 15)
    @ApiModelProperty(value = "营业执照编号")
	private java.lang.String businessLicense;
	/**组织机构代码号、社会信用代码号或工商注册号*/
	@Excel(name = "组织机构代码号、社会信用代码号或工商注册号", width = 15)
    @ApiModelProperty(value = "组织机构代码号、社会信用代码号或工商注册号")
	private java.lang.String orgCode;
}
