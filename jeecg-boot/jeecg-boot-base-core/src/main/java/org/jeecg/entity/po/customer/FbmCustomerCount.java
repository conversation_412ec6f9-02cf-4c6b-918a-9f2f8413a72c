package org.jeecg.entity.po.customer;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;

/**
 * @Description: 客户管理页面数据统计
 * @Author: jeecg-boot
 * @Date:   2025-08-27
 * @Version: V1.0
 */
@Data
@TableName("fbm_customer_count")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="fbm_customer_count对象", description="客户管理页面数据统计")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class FbmCustomerCount {

	/**客户id*/
	@Excel(name = "客户id", width = 15)
    @ApiModelProperty(value = "客户id")
	@TableId(type = IdType.INPUT)
	private java.lang.Long customerId;
	/**监测作品数*/
	@Excel(name = "监测作品数", width = 15)
    @ApiModelProperty(value = "监测作品数")
	private java.lang.String worksCount;
	/**侵权数*/
	@Excel(name = "侵权数", width = 15)
    @ApiModelProperty(value = "侵权数")
	private java.lang.String tortCount;
	/**站点数*/
	@Excel(name = "站点数", width = 15)
    @ApiModelProperty(value = "站点数")
	private java.lang.String siteCount;
}
