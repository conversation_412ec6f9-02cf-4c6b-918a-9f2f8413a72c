package org.jeecg.modules.platform.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.entity.po.customer.FbmCustomerTortMonthCount;
import org.jeecg.modules.platform.mapper.FbmCustomerTortMonthCountMapper;
import org.jeecg.modules.platform.service.IFbmCustomerTortMonthCountService;
import org.springframework.stereotype.Service;

/**
 * @Description: 客户管理页面月份侵权数数据统计
 * @Author: jeecg-boot
 * @Date:   2025-08-28
 * @Version: V1.0
 */
@Service
public class FbmCustomerTortMonthCountServiceImpl extends ServiceImpl<FbmCustomerTortMonthCountMapper, FbmCustomerTortMonthCount> implements IFbmCustomerTortMonthCountService {

}
