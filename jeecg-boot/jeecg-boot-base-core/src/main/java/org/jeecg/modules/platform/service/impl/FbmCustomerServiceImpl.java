package org.jeecg.modules.platform.service.impl;

import org.jeecg.entity.po.customer.FbmCustomer;
import org.jeecg.modules.platform.mapper.FbmCustomerMapper;
import org.jeecg.modules.platform.service.IFbmCustomerService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 客户管理
 * @Author: jeecg-boot
 * @Date:   2025-08-26
 * @Version: V1.0
 */
@Service
public class FbmCustomerServiceImpl extends ServiceImpl<FbmCustomerMapper, FbmCustomer> implements IFbmCustomerService {

}
