package org.jeecg.modules.platform.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.jeecg.entity.FbmTortDataVideo;
import org.jeecg.entity.bo.*;
import org.jeecg.entity.param.MiguWorksSiteOfflinePrParam;
import org.jeecg.entity.param.SelectVvParam;
import org.jeecg.entity.vo.account.AccountWorkDetailVo;

import java.util.List;
import java.util.Map;

/**
 * @Description: 视频侵权表
 * @Author: jeecg-boot
 * @Date: 2024-09-05
 * @Version: V1.0
 */
@DS(value = "db-239")
public interface FbmTortDataVideoMapper extends BaseMapper<FbmTortDataVideo> {

	@Select(" <script> " +
			"  SELECT IFNULL(sum(IFNULL(click_num,0) ),0) from fbm_tort_data_video " +
			"  where tort_is_del=1 and works_id in " +
			"   <foreach collection=\"param.worksIds\" index=\"index\" item=\"item\" open=\"(\" close=\")\" separator=\",\">\n" +
			"                #{item}\n" +
			"   </foreach>  " +
			" </script>")
	long sumVv(@Param(value = "param") SelectVvParam param);


	@Select(" <script> " +
			"  SELECT b.site_show_name as siteShowName,count(v.id) as tortNum from fbm_tort_data_video v " +
			"  inner join fbm_site_base b on b.id=v.site_base_id   " +
			"  where tort_is_del=1 and works_id in " +
			"   <foreach collection=\"worksIds\" index=\"index\" item=\"item\" open=\"(\" close=\")\" separator=\",\">\n" +
			"                #{item}\n" +
			"   </foreach>  group by b.site_show_name  " +
			" </script>")
	List<SiteTopBo> groupBySiteShowName(@Param(value = "worksIds") List<Long> worksIds);


	@Select(" <script> " +
			"  SELECT publisher,count(v.id) as tortNum from fbm_tort_data_video v " +
			"  where tort_is_del=1 and works_id in " +
			"   <foreach collection=\"worksIds\" index=\"index\" item=\"item\" open=\"(\" close=\")\" separator=\",\">\n" +
			"                #{item}\n" +
			"   </foreach> and publisher is not null and length(publisher)!=0  group by publisher  " +
			" </script>")
	List<PublisherTopBo> groupByPublisher(@Param(value = "worksIds") List<Long> worksIds);


	@Select(" <script> " +
			"  SELECT IFNULL(sum(IFNULL(click_num,0) ),0) as vvNum,url_md5,url,publisher,title from fbm_tort_data_video " +
			"  where tort_is_del=1 and works_id in " +
			"   <foreach collection=\"worksIds\" index=\"index\" item=\"item\" open=\"(\" close=\")\" separator=\",\">\n" +
			"                #{item}\n" +
			"   </foreach>  group by url_md5 " +
			" </script>")
	List<UrlVvBo> groupByUrl(@Param(value = "worksIds") List<Long> worksIds);


	@Select(" <script> " +
			"  SELECT b.site_show_name as siteShowName,count(v.id) as mailOfflineNum from fbm_tort_data_video v " +
			"  inner join fbm_site_base b on b.id=v.site_base_id   " +
			"  where tort_is_del=1" +
			"  and works_id in " +
			"   <foreach collection=\"param.worksIds\" index=\"index\" item=\"item\" open=\"(\" close=\")\" separator=\",\">\n" +
			"                #{item}\n" +
			"   </foreach>" +
			"   and b.site_show_name in " +
			"   <foreach collection=\"param.siteShowNames\" index=\"index\" item=\"item\" open=\"(\" close=\")\" separator=\",\">\n" +
			"                #{item}\n" +
			"   </foreach>" +
			"  and tort_status not in (0,2,3,4) and  v.tort_first_send_mail_time is not null " +
			" and v.tort_offline_date is not null  " +
			" and tort_offline_date &lt; date_add(tort_first_send_mail_time, interval #{param.hour}  HOUR)     " +
			"      group by b.site_show_name  " +
			" </script>")
	List<MiguWorksSiteMailOfflineBo> getSiteMailOfflineCount(@Param(value = "param") MiguWorksSiteOfflinePrParam param);


	@Select(" <script> " +
			"  SELECT b.site_show_name as siteShowName,count(v.id) as mailOnlineNum from fbm_tort_data_video v " +
			"  inner join fbm_site_base b on b.id=v.site_base_id   " +
			"  where tort_is_del=1 and works_id in " +
			"   <foreach collection=\"param.worksIds\" index=\"index\" item=\"item\" open=\"(\" close=\")\" separator=\",\">\n" +
			"                #{item}\n" +
			"   </foreach>" +
			"   and b.site_show_name in " +
			"   <foreach collection=\"param.siteShowNames\" index=\"index\" item=\"item\" open=\"(\" close=\")\" separator=\",\">\n" +
			"                #{item}\n" +
			"   </foreach>" +
			" and tort_status in (0,2,3,4) and  v.tort_first_send_mail_time is not null " +
			" and v.tort_last_send_mail_time is not null  " +
			"  and tort_last_send_mail_time &lt; date_add(tort_first_send_mail_time, interval #{param.hour} HOUR)     " +
			"      group by b.site_show_name  " +
			" </script>")
	List<MiguWorksSiteMailOnlineBo> getSiteMailOnlineCount(@Param(value = "param") MiguWorksSiteOfflinePrParam param);


	@Select("select ftdv.* " +
			"from fbm_tort_data_video ftdv " +
			"inner join fbm_site_base fsb on ftdv.site_base_id = fsb.id " +
			"inner join fbo_client_sheet_work fcsw on ftdv.works_id = fcsw.base_works_id " +
			"where ftdv.id > #{lastId} and fsb.site_show_name = #{siteShowName} " +
			"and " +
			"   case " +
			"       when ftdv.publisher_id is not null and ftdv.publisher_id != '' then ftdv.publisher_id = #{publisherId} " +
			"       else ftdv.publisher = #{publisher} " +
			"   end")
	List<FbmTortDataVideo> listByPublisher(@Param("lastId") long lastId,
										   @Param("siteShowName") String siteShowName, @Param("publisherId") String publisherId, @Param("publisher") String publisher);

	@Select("select count(*) as clue_num, " +
			"count(if(tort_is_del = 1, 1, null)) as valid_clue_num, " +
			"count(if(tort_is_del != 1, 1, null)) as invalid_clue_num " +
			"from fbm_tort_data_video ftdv " +
			"where works_id = #{worksId}")
	AccountWorkDetailVo.TortClueInfo selectTortClueInfo(@Param("worksId") Long worksId);


	@Select("<script>" +
			"SELECT COUNT(*) as tortCount, COUNT(DISTINCT site_base_id) as siteCount " +
			"FROM fbm_tort_data_video " +
			"WHERE tort_is_del = 1 " +
			"<if test='worksIds != null and worksIds.size() > 0'>" +
			"   AND works_id IN " +
			"   <foreach collection='worksIds' item='id' open='(' separator=',' close=')'>" +
			"       #{id}" +
			"   </foreach>" +
			"</if>" +
			"</script>")
	Map<String, Long> getCountNum(@Param(value = "worksIds") List<Long> worksIds);
}
