package org.jeecg.modules.platform.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.jeecg.entity.FbmTortDataIptv;

import java.util.List;
import java.util.Map;

/**
 * @Description: iptv侵权表
 * @Author: jeecg-boot
 * @Date:   2024-09-05
 * @Version: V1.0
 */
@DS(value = "db-239")
public interface FbmTortDataIptvMapper extends BaseMapper<FbmTortDataIptv> {


    List<Long> selectWorksIds();

    @Select("<script>" +
            "SELECT COUNT(*) as tortCount " +
            "FROM fbm_tort_data_iptv " +
            "WHERE tort_is_del = 1 " +
            "<if test='worksIds != null and worksIds.size() > 0'>" +
            "   AND works_id IN " +
            "   <foreach collection='worksIds' item='id' open='(' separator=',' close=')'>" +
            "       #{id}" +
            "   </foreach>" +
            "</if>" +
            "</script>")
    Map<String, Long> getCountNum(@Param(value = "worksIds") List<Long> worksIds);
}
